#!/usr/bin/env python3
"""
split_markdown.py — split a Markdown file into multiple files by headings.

Features
--------
- Splits on ATX headings (#, ##, etc.) and Setext headings (underlines === / ---).
- Lets you choose which heading levels start a new file (e.g., 1 or 1,2).
- Ignores headings that appear inside fenced code blocks.
- Optional: treat YAML front matter specially and copy it into each split file.
- Extracts clean, OS-safe filenames from headings; ensures uniqueness.
- Can nest H2 files inside per-H1 folders for tidy structure (optional).
- Preserves original line wrapping; no content is modified.
- Dry-run mode to preview what would be created.

Usage
-----
    python split_markdown.py INPUT.md [options]

Examples
--------
    # split on H1 and H2, put results into ./split_files (default)
    python split_markdown.py input.md

    # split only on H1
    python split_markdown.py input.md --levels 1

    # split on H1/H2 and place H2 files under their H1 folder
    python split_markdown.py input.md --levels 1,2 --nest

    # include YAML front matter at the top of every split file
    python split_markdown.py input.md --carry-frontmatter
"""
import argparse
import os
import re
import sys
from typing import List, Optional, Tuple

FENCE_PATTERN = re.compile(r"^(?P<fence>`{3,}|~{3,})(?P<info>.*)$")
ATX_HEADING = re.compile(r"^(?P<hashes>#{1,6})\s+(?P<text>.*?)(?:\s+#+)?\s*$")
# Setext headings are two lines: text line then underline of === (H1) or --- (H2)
SETEXT_UNDERLINE = re.compile(r"^(?P<underline>=+|-+)\s*$")

ILLEGAL_FILENAME_CHARS = re.compile(r"[^A-Za-z0-9._-]")
MULTI_UNDERSCORES = re.compile(r"_+")

def slugify(text: str, limit: int = 80) -> str:
    """
    Convert heading text to a filesystem-friendly slug.
    """
    text = text.strip()
    # Remove surrounding markdown emphasis/backticks
    text = re.sub(r"`+", "", text)
    text = re.sub(r"^\**|\**$", "", text)
    text = re.sub(r"^\*+|\*+$", "", text)
    # Replace spaces and illegal chars with underscores
    text = ILLEGAL_FILENAME_CHARS.sub("_", text)
    text = MULTI_UNDERSCORES.sub("_", text).strip("_")
    if not text:
        text = "section"
    return text[:limit]

def unique_path(base_dir: str, filename: str) -> str:
    """
    Ensure the path is unique by appending a numeric suffix if needed.
    """
    root, ext = os.path.splitext(filename)
    candidate = filename
    i = 2
    while os.path.exists(os.path.join(base_dir, candidate)):
        candidate = f"{root}_{i}{ext}"
        i += 1
    return os.path.join(base_dir, candidate)

def parse_levels(levels_arg: str) -> List[int]:
    try:
        levels = [int(x.strip()) for x in levels_arg.split(",") if x.strip()]
    except ValueError:
        raise argparse.ArgumentTypeError("Levels must be a comma-separated list of integers, e.g. '1,2'.")
    for lv in levels:
        if lv < 1 or lv > 6:
            raise argparse.ArgumentTypeError("Levels must be between 1 and 6.")
    return sorted(set(levels))

def detect_yaml_front_matter(lines: List[str]) -> Tuple[List[str], int]:
    """
    If file starts with YAML front matter delimited by '---' (and optionally ends with '---' or '...'),
    return (front_matter_lines, index_after_front_matter). Otherwise return ([], 0).
    """
    if not lines:
        return [], 0
    if lines[0].strip() != "---":
        return [], 0
    fm = [lines[0]]
    i = 1
    while i < len(lines):
        fm.append(lines[i])
        if lines[i].strip() in ("---", "..."):
            return fm, i + 1
        i += 1
    # Unterminated front matter: treat as none
    return [], 0

def iter_markdown_blocks(lines: List[str]):
    """
    Iterate through lines, yielding tuples describing blocks of interest.
    Yields:
        ("fence", start_idx, end_idx_exclusive)
        ("atx", level, text, line_idx)
        ("setext", level, text, text_line_idx, underline_idx)
        ("line", line_idx) for other lines (single line indication)
    """
    in_fence = False
    fence_char = None
    fence_start = None

    i = 0
    while i < len(lines):
        line = lines[i]
        if not in_fence:
            m = FENCE_PATTERN.match(line)
            if m:
                in_fence = True
                fence_char = m.group("fence")[0]
                fence_start = i
                i += 1
                continue

            m = ATX_HEADING.match(line)
            if m:
                level = len(m.group("hashes"))
                text = m.group("text").strip()
                yield ("atx", level, text, i)
                i += 1
                continue

            # Setext detection: need at least one more line
            if i + 1 < len(lines) and lines[i].strip() and not lines[i].lstrip().startswith("#"):
                m2 = SETEXT_UNDERLINE.match(lines[i+1])
                if m2:
                    underline = m2.group("underline")
                    level = 1 if underline.startswith("=") else 2
                    text = lines[i].strip()
                    yield ("setext", level, text, i, i+1)
                    i += 2
                    continue

            # Regular line
            yield ("line", i)
            i += 1
        else:
            # inside fenced code block, look for closing fence of same char (` or ~)
            if line.startswith(fence_char * 3):
                # close fence
                yield ("fence", fence_start, i + 1)
                in_fence = False
                fence_char = None
                fence_start = None
                i += 1
            else:
                i += 1

    # If file ended while still in fence, close it
    if in_fence and fence_start is not None:
        yield ("fence", fence_start, len(lines))

def write_section(path: str, content_lines: List[str], front_matter: Optional[List[str]], carry_frontmatter: bool, dry_run: bool):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    if dry_run:
        return
    with open(path, "w", encoding="utf-8") as f:
        if carry_frontmatter and front_matter:
            f.writelines(front_matter)
            if front_matter and not front_matter[-1].endswith("\n"):
                f.write("\n")
        f.writelines(content_lines)

def split_markdown(
    input_file: str,
    output_dir: str = "split_files",
    levels: List[int] = [1,2],
    nest: bool = False,
    prefix_numbers: bool = True,
    carry_frontmatter: bool = False,
    keep_empty: bool = False,
    dry_run: bool = False,
) -> None:
    try:
        with open(input_file, "r", encoding="utf-8") as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.", file=sys.stderr)
        sys.exit(1)

    fm_lines, start_idx = detect_yaml_front_matter(lines)
    content = lines[start_idx:]

    if not os.path.exists(output_dir) and not dry_run:
        os.makedirs(output_dir, exist_ok=True)

    section_lines: List[str] = []
    section_path: Optional[str] = None
    h1_folder: Optional[str] = None
    file_counter = 0

    def start_new_file(level: int, heading_text: str, heading_line: str):
        nonlocal section_lines, section_path, file_counter, h1_folder
        # write current section if any
        if section_path is not None:
            if section_lines or keep_empty:
                write_section(section_path, section_lines, fm_lines, carry_frontmatter, dry_run)
        # compute new path
        file_counter += 1
        slug = slugify(heading_text)
        filename = f"{file_counter:02d}_{slug}.md" if prefix_numbers else f"{slug}.md"
        base_dir = output_dir

        if nest and level == 2 and h1_folder:
            base_dir = os.path.join(output_dir, h1_folder)
        elif nest and level == 1:
            h1_folder = slug

        if not os.path.exists(base_dir) and not dry_run:
            os.makedirs(base_dir, exist_ok=True)

        path = unique_path(base_dir, filename)
        print(f"-> New file: {path}")
        section_path = path
        section_lines = [heading_line]

    # iterate through blocks
    i = 0
    while i < len(content):
        for block in iter_markdown_blocks(content[i:]):
            if block[0] == "atx":
                level, text, line_idx = block[1], block[2], i + block[3]
                heading_line = content[line_idx]
                if level in levels:
                    start_new_file(level, text, heading_line)
                else:
                    if section_path is None:
                        start_new_file(level=levels[0], heading_text="preface", heading_line="# Preface\n")
                    section_lines.append(heading_line)
                i = line_idx + 1
                break
            elif block[0] == "setext":
                level, text, text_idx, underline_idx = block[1], block[2], i + block[3], i + block[4]
                heading_line = content[text_idx] + content[underline_idx]
                if level in levels:
                    start_new_file(level, text, heading_line)
                else:
                    if section_path is None:
                        start_new_file(level=levels[0], heading_text="preface", heading_line="# Preface\n")
                    section_lines.append(heading_line)
                i = underline_idx + 1
                break
            elif block[0] == "fence":
                start, end = i + block[1], i + block[2]
                if section_path is None:
                    start_new_file(level=levels[0], heading_text="preface", heading_line="# Preface\n")
                section_lines.extend(content[start:end])
                i = end
                break
            elif block[0] == "line":
                line_idx = i + block[1]
                if section_path is None:
                    start_new_file(level=levels[0], heading_text="preface", heading_line="# Preface\n")
                section_lines.append(content[line_idx])
                i = line_idx + 1
                break
        else:
            break

    # flush last section
    if section_path is not None and (section_lines or keep_empty):
        write_section(section_path, section_lines, fm_lines, carry_frontmatter, dry_run)

    print("\nDone.")
    print(f"Output directory: {output_dir}")
    if dry_run:
        print("(dry run) No files were written.")

def main():
    p = argparse.ArgumentParser(description="Split a Markdown file into multiple files by headings.")
    p.add_argument("input", help="Path to the input Markdown file")
    p.add_argument("-o", "--output-dir", default="split_files", help="Directory to write split files (default: split_files)")
    p.add_argument("--levels", default="1,2", help="Comma-separated heading levels to split on (e.g., '1' or '1,2')")
    p.add_argument("--nest", action="store_true", help="Nest H2 files into folders named after their parent H1")
    p.add_argument("--no-prefix", action="store_true", help="Do not prefix filenames with running numbers")
    p.add_argument("--carry-frontmatter", action="store_true", help="Copy YAML front matter into each split file")
    p.add_argument("--keep-empty", action="store_true", help="Write out empty sections (normally skipped)")
    p.add_argument("--dry-run", action="store_true", help="Show what would be created without writing files")
    args = p.parse_args()

    levels = parse_levels(args.levels)
    prefix_numbers = not args.no_prefix

    split_markdown(
        input_file=args.input,
        output_dir=args.output_dir,
        levels=levels,
        nest=args.nest,
        prefix_numbers=prefix_numbers,
        carry_frontmatter=args.carry_frontmatter,
        keep_empty=args.keep_empty,
        dry_run=args.dry_run,
    )

if __name__ == "__main__":
    main()