import sys
import re
import os

def clean_filename(heading):
    """Cleans a heading to create a valid filename."""
    # Remove leading hashes and spaces
    text = heading.lstrip('# ').strip()
    # Replace spaces and special characters with underscores
    text = re.sub(r'[\s\W]+', '_', text)
    # Truncate to a reasonable length
    return text[:60]

def split_markdown(input_file):
    """Splits a markdown file by H1 and H2 headings."""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        return

    output_file = None
    output_lines = []
    file_counter = 0

    # Create a subfolder for the split files to keep things tidy
    output_dir = "split_files"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for line in lines:
        # Check for H1 or H2 headings
        if line.startswith('# ') or line.startswith('## '):
            # If we were already writing to a file, save and close it
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.writelines(output_lines)
            
            # Create a new filename from the heading
            file_counter += 1
            filename_base = clean_filename(line)
            new_filename = f"{file_counter:02d}_{filename_base}.md"
            output_file = os.path.join(output_dir, new_filename)
            
            print(f"Creating new file: {output_file}")
            
            # Start a new list of lines for the new file
            output_lines = [line]
        elif output_file:
            # If we are in a section, append the current line
            output_lines.append(line)
    
    # Write the very last file
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(output_lines)

    print("\nSplitting complete!")
    print(f"Files are located in the '{output_dir}' folder.")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python split_document.py <your_markdown_file.md>")
    else:
        split_markdown(sys.argv[1])