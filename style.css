/* A simple stylesheet to make the HTML look more like a document */
body {
    font-family: 'Times New Roman', Times, serif; /* Or your preferred document font */
    line-height: 1.6;
    max-width: 800px; /* Set a max-width to mimic a page */
    margin: 2em auto; /* Center the content on the page */
    padding: 2em;
    border: 1px solid #ddd;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h1, h2, h3 {
    color: #333;
    line-height: 1.2;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1em;
}

th, td {
    border: 1px solid #ccc;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

img {
    max-width: 100%;
    height: auto; /* Make images responsive */
}
table {
  width: 100%;
  border-collapse: collapse; /* Merges cell borders for a clean look */
  margin-bottom: 1.5em; /* Adds some space below the table */
}

th, td {
  border: 1px solid #000000; /* A solid, 1px black border */
  padding: 8px; /* Adds some space inside the cells */
  text-align: left;
}

th {
  background-color: #f2f2f2; /* A light grey background for header cells */
}